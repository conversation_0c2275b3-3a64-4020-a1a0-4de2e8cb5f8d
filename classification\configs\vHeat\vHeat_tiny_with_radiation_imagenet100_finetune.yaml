# vHeat-Tiny模型配置（带热辐射机制）- ImageNet100版本 - 继续训练/微调版本
# 基于已训练100轮的模型继续训练，使用更低的学习率

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_tiny_with_radiation_imagenet100_finetune
  NUM_CLASSES: 100  # ImageNet100有100个类别
  DROP_PATH_RATE: 0.05  # 降低dropout以稳定微调

  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.08  # 保持原有辐射强度

  # vHeat特定参数
  VHEAT:
    PATCH_SIZE: 4
    IN_CHANS: 3
    EMBED_DIM: 96
    DEPTHS: [2, 2, 6, 2]
    MLP_RATIO: 4.0
    PATCH_NORM: True
    POST_NORM: True
    LAYER_SCALE: null

# 数据配置
DATA:
  BATCH_SIZE: 128  # 保持原有批量大小
  DATA_PATH: '/path/to/imagenet100'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 4

# 数据增强配置 - 微调时使用更轻的数据增强
AUG:
  AUTO_AUGMENT: 'rand-m5-mstd0.5-inc1'  # 降低增强强度
  REPROB: 0.1  # 降低随机擦除概率
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.4  # 降低mixup强度
  CUTMIX: 0.6  # 降低cutmix强度
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置 - 微调专用设置
TRAIN:
  START_EPOCH: 0  # 从0开始计数，但会从checkpoint恢复
  EPOCHS: 50  # 继续训练50轮
  WARMUP_EPOCHS: 5  # 短暂的warmup
  WEIGHT_DECAY: 0.02  # 降低权重衰减
  BASE_LR: 1e-4  # 大幅降低学习率，原来是2e-3
  WARMUP_LR: 1e-7  # 更低的warmup学习率
  MIN_LR: 1e-7  # 更低的最小学习率
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度 - 使用更平缓的衰减
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 1
  USE_CHECKPOINT: False
  
  # EMA - 禁用EMA避免恢复问题
  MODEL_EMA: False
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False
  SHUFFLE: False

# 其他配置
OUTPUT: './output_finetune'
PRINT_FREQ: 20
SAVE_FREQ: 5  # 更频繁地保存检查点
SEED: 42
TAG: 'vheat_tiny_with_radiation_imagenet100_finetune'

# 混合精度训练
AMP_ENABLE: True
AMP_OPT_LEVEL: ''
ENABLE_AMP: False

# 其他设置
EVAL_MODE: False
FUSED_LAYERNORM: False
FUSED_WINDOW_PROCESS: False
THROUGHPUT_MODE: False
