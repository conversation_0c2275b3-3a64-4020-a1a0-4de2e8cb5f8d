#!/bin/bash

# ImageNet-1K 训练示例脚本
# 请根据您的实际环境修改数据集路径和GPU配置

# 设置数据集路径 (请修改为您的实际路径)
IMAGENET_PATH="/path/to/your/imagenet"

# 检查数据集路径是否存在
if [ ! -d "$IMAGENET_PATH" ]; then
    echo "错误: ImageNet数据集路径不存在: $IMAGENET_PATH"
    echo "请修改脚本中的 IMAGENET_PATH 变量为您的实际数据集路径"
    exit 1
fi

echo "ImageNet-1K 训练示例"
echo "数据集路径: $IMAGENET_PATH"
echo "================================"

# 示例1: 基础训练 - Tiny模型
echo "示例1: 基础训练 - Tiny模型 (4 GPU)"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size tiny \
    --batch-size 128 \
    --gpus 4 \
    --epochs 300 \
    --output ./experiments/tiny_baseline

echo ""
echo "================================"

# 示例2: 带早停的训练 - Small模型
echo "示例2: 带早停的训练 - Small模型 (8 GPU)"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size small \
    --batch-size 64 \
    --gpus 8 \
    --early-stopping \
    --patience 20 \
    --min-delta 0.0001 \
    --output ./experiments/small_early_stop

echo ""
echo "================================"

# 示例3: 热辐射机制 + 早停 - Tiny模型
echo "示例3: 热辐射机制 + 早停 - Tiny模型 (4 GPU)"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.05 \
    --batch-size 128 \
    --gpus 4 \
    --early-stopping \
    --patience 15 \
    --output ./experiments/tiny_radiation_early_stop

echo ""
echo "================================"

# 示例4: Base模型训练 (需要更多GPU内存)
echo "示例4: Base模型训练 (8 GPU, 需要大内存GPU)"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size base \
    --batch-size 32 \
    --gpus 8 \
    --early-stopping \
    --patience 25 \
    --output ./experiments/base_model

echo ""
echo "================================"

# 示例5: 单GPU训练 (适合调试)
echo "示例5: 单GPU训练 - Tiny模型 (适合调试)"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size tiny \
    --batch-size 64 \
    --single-gpu \
    --early-stopping \
    --patience 10 \
    --epochs 50 \
    --output ./experiments/tiny_single_gpu_debug

echo ""
echo "================================"

# 示例6: 从检查点恢复训练
echo "示例6: 从检查点恢复训练"
echo "# 首先需要有一个已存在的检查点文件"
echo "# python train_imagenet1k.py \\"
echo "#     --data-path $IMAGENET_PATH \\"
echo "#     --model-size tiny \\"
echo "#     --resume ./experiments/tiny_baseline/ckpt_epoch_100.pth \\"
echo "#     --early-stopping \\"
echo "#     --output ./experiments/tiny_resumed"

echo ""
echo "================================"

# 示例7: 自定义学习率和权重衰减
echo "示例7: 自定义学习率和权重衰减"
python train_imagenet1k.py \
    --data-path $IMAGENET_PATH \
    --model-size small \
    --batch-size 64 \
    --gpus 4 \
    --lr 1e-3 \
    --weight-decay 0.1 \
    --warmup-epochs 30 \
    --early-stopping \
    --patience 20 \
    --output ./experiments/small_custom_lr

echo ""
echo "所有示例命令已显示完成!"
echo "请根据您的硬件配置选择合适的示例运行。"
echo ""
echo "注意事项:"
echo "1. 确保您有足够的GPU内存"
echo "2. 根据GPU数量调整batch-size"
echo "3. ImageNet-1K训练需要较长时间，建议使用早停机制"
echo "4. 监控训练日志以调整超参数"
