MODEL:
  TYPE: vHeat_with_radiation
  NAME: vHeat_small_with_radiation_imagenet1k
  DROP_PATH_RATE: 0.2
  NUM_CLASSES: 1000  # ImageNet-1K有1000个类别
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.05  # ImageNet-1K使用较小的辐射强度
  VHEAT:
    EMBED_DIM: 192
    DEPTHS: [ 2, 2, 18, 2 ]
    POST_NORM: False

TRAIN:
  BASE_LR: 5.e-4
  WEIGHT_DECAY: 0.05
  EPOCHS: 300  # ImageNet-1K通常需要更多训练轮数
  WARMUP_EPOCHS: 20
  MIN_LR: 5.e-6
  LR_SCHEDULER:
    NAME: 'cosine'
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1.e-8
    BETAS: [0.9, 0.999]
  CLIP_GRAD: 5.0
  
  # 早停配置
  EARLY_STOPPING:
    ENABLE: False  # 默认关闭，通过命令行参数启用
    PATIENCE: 20
    MIN_DELTA: 0.0001
    MODE: 'max'  # 监控验证准确率，越大越好
    RESTORE_BEST_WEIGHTS: True

DATA:
  IMG_SIZE: 224
  DATASET: 'imagenet'  # 使用imagenet数据集格式
  BATCH_SIZE: 64  # 可以根据GPU内存调整
  INTERPOLATION: 'bicubic'
  PIN_MEMORY: True
  NUM_WORKERS: 8

AUG:
  COLOR_JITTER: 0.4
  AUTO_AUGMENT: 'rand-m9-mstd0.5-inc1'
  REPROB: 0.25
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.8
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

SAVE_FREQ: 10
PRINT_FREQ: 100
SEED: 0
