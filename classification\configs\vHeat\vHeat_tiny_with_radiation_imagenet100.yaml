# vHeat-Tiny模型配置（带热辐射机制）- ImageNet100版本
# 基于原始vHeat-Tiny ImageNet100配置，添加热辐射功能

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_tiny_with_radiation_imagenet100
  NUM_CLASSES: 100  # ImageNet100有100个类别
  DROP_PATH_RATE: 0.1  # 降低dropout以适应小数据集

  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.08  # 对小数据集使用较小的辐射强度

  # vHeat特定参数
  VHEAT:
    PATCH_SIZE: 4
    IN_CHANS: 3
    EMBED_DIM: 96
    DEPTHS: [2, 2, 6, 2]  # 减少深度以适应ImageNet100
    MLP_RATIO: 4.0
    PATCH_NORM: True
    POST_NORM: True
    LAYER_SCALE: null

# 数据配置
DATA:
  BATCH_SIZE: 128  # 适合ImageNet100的批量大小
  DATA_PATH: '/path/to/imagenet100'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 4  # 减少worker数量

# 数据增强配置（适合小数据集）
AUG:
  AUTO_AUGMENT: 'rand-m7-mstd0.5-inc1'  # 稍微减弱增强
  REPROB: 0.2  # 降低随机擦除概率
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.6  # 降低mixup强度
  CUTMIX: 0.8  # 降低cutmix强度
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置
TRAIN:
  START_EPOCH: 0
  EPOCHS: 100  # ImageNet100通常训练100-200轮
  WARMUP_EPOCHS: 10  # 减少warmup轮数
  WEIGHT_DECAY: 0.05  # 降低权重衰减
  BASE_LR: 2e-3  # 适中的学习率
  WARMUP_LR: 1e-6
  MIN_LR: 1e-6
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 1
  USE_CHECKPOINT: False
  
  # EMA (Exponential Moving Average) - 禁用EMA
  MODEL_EMA: False
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False

# 输出配置
OUTPUT: './output'
TAG: 'vheat_tiny_with_radiation_imagenet100'
SAVE_FREQ: 10  # 每10轮保存一次
PRINT_FREQ: 20

# 其他配置
SEED: 42
EVAL_MODE: False
THROUGHPUT_MODE: False

# ImageNet100特定配置说明
# - 类别数：100（而非1000）
# - 训练轮数：100-200轮通常足够
# - 学习率：可以稍微高一些，因为数据集较小
# - 数据增强：适度减弱，避免过拟合
# - 辐射强度：0.08，比完整ImageNet的0.1稍小

# 热辐射机制在小数据集上的优势：
# 1. 更好的特征复用：通过通道间交互提升表达能力
# 2. 正则化效应：辐射机制有助于防止过拟合
# 3. 更强的泛化：物理启发的设计提升模型鲁棒性
