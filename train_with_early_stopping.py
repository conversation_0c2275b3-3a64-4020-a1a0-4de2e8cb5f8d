#!/usr/bin/env python3
"""
带早停策略的继续训练脚本
使用方法:
python train_with_early_stopping.py --data-path /path/to/your/imagenet100 --checkpoint ./output/ckpt_epoch_99.pth
"""


import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Continue training vHeat with early stopping')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to checkpoint file to resume from')
    parser.add_argument('--batch-size', type=int, default=128,
                        help='Batch size per GPU (default: 128)')
    parser.add_argument('--output', type=str, default='./output_early_stop',
                        help='Output directory (default: ./output_early_stop)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--model-size', type=str, default='tiny',
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    
    # 训练相关参数
    parser.add_argument('--epochs', type=int, default=50,
                        help='Number of additional training epochs (default: 50)')

    # 学习率相关参数
    parser.add_argument('--base-lr', type=float, default=1e-4,
                        help='Base learning rate for fine-tuning (default: 1e-4)')
    parser.add_argument('--min-lr', type=float, default=1e-7,
                        help='Minimum learning rate (default: 1e-7)')
    parser.add_argument('--weight-decay', type=float, default=0.02,
                        help='Weight decay for fine-tuning (default: 0.02)')
    parser.add_argument('--warmup-epochs', type=int, default=5,
                        help='Warmup epochs (default: 5)')
    
    # 早停相关参数
    parser.add_argument('--patience', type=int, default=15,
                        help='Early stopping patience in epochs (default: 15)')
    parser.add_argument('--min-delta', type=float, default=0.0001,
                        help='Minimum improvement threshold (default: 0.0001 = 0.01%)')
    
    # 其他参数
    parser.add_argument('--radiation-strength', type=float, default=0.08,
                        help='Radiation strength (default: 0.08)')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU training (no distributed)')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')

    args = parser.parse_args()
    
    # 检查checkpoint文件
    if not os.path.exists(args.checkpoint):
        raise ValueError(f"Checkpoint file does not exist: {args.checkpoint}")
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    print(f"Found {val_classes} classes in validation set")
    
    if train_classes != val_classes:
        print(f"Warning: Train and validation have different number of classes!")
    
    # 使用早停专用配置文件
    config_file = 'classification/configs/vHeat/vHeat_tiny_with_radiation_imagenet100_early_stop.yaml'
    
    # 构建训练命令
    if args.single_gpu or args.gpus == 1:
        # 单GPU训练，不使用分布式
        cmd = [
            'python', 'classification/main_with_early_stopping.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--local_rank', '0'
        ]
    else:
        # 多GPU分布式训练，使用torchrun
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=' + str(args.gpus),
            '--master_addr=127.0.0.1',
            '--master_port=29503',  # 使用不同的端口避免冲突
            'classification/main_with_early_stopping.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output
        ]
    
    # 添加resume参数
    cmd.extend(['--resume', args.checkpoint])
    
    # 添加eval-only参数
    if args.eval_only:
        cmd.append('--eval')

    # 禁用EMA（推荐用于恢复训练）
    cmd.extend(['--model_ema', 'False'])
    
    # 添加早停参数
    cmd.extend(['--early-stopping'])
    cmd.extend(['--patience', str(args.patience)])
    cmd.extend(['--min-delta', str(args.min_delta)])

    # 添加自定义配置选项
    opts = [
        '--opts',
        'TRAIN.EPOCHS', str(args.epochs),
        'TRAIN.BASE_LR', str(args.base_lr),
        'TRAIN.MIN_LR', str(args.min_lr),
        'TRAIN.WEIGHT_DECAY', str(args.weight_decay),
        'TRAIN.WARMUP_EPOCHS', str(args.warmup_epochs),
        'MODEL.NUM_CLASSES', str(train_classes),
        'MODEL.ENABLE_RADIATION', 'True',
        'MODEL.RADIATION_STRENGTH', str(args.radiation_strength),
        'OUTPUT', args.output
    ]

    cmd.extend(opts)
    
    print("=" * 80)
    print("🚀 早停策略继续训练配置:")
    print(f"  📁 检查点文件: {args.checkpoint}")
    print(f"  📂 数据集路径: {args.data_path}")
    print(f"  📤 输出目录: {args.output}")
    print(f"  🔧 模型大小: {args.model_size}")
    print(f"  📊 批量大小: {args.batch_size}")
    print(f"  🔄 继续训练轮数: {args.epochs}")
    print(f"  📈 基础学习率: {args.base_lr}")
    print(f"  📉 最小学习率: {args.min_lr}")
    print(f"  ⚖️  权重衰减: {args.weight_decay}")
    print(f"  🔥 预热轮数: {args.warmup_epochs}")
    print(f"  ☢️  辐射强度: {args.radiation_strength}")
    print(f"  🖥️  GPU数量: {args.gpus}")
    print()
    print("🛑 早停策略配置:")
    print(f"  ⏳ 耐心值: {args.patience} epochs")
    print(f"  📏 最小改善阈值: {args.min_delta} ({args.min_delta*100:.2f}%)")
    print(f"  📋 监控指标: 验证准确率 (越高越好)")
    print(f"  💾 自动保存最佳权重: 是")
    print(f"  🔄 自动恢复最佳权重: 是")
    print("=" * 80)
    print()
    
    print("🔧 执行命令:")
    print(' '.join(cmd))
    print()

    # 设置单GPU训练的环境变量
    if args.single_gpu or args.gpus == 1:
        os.environ['RANK'] = '0'
        os.environ['WORLD_SIZE'] = '1'
        os.environ['MASTER_ADDR'] = '127.0.0.1'
        os.environ['MASTER_PORT'] = '29500'
    
    print("📝 早停策略说明:")
    print("1. 监控阶段:")
    print("   - 每个epoch后检查验证准确率")
    print(f"   - 如果准确率提升超过{args.min_delta*100:.2f}%，重置等待计数器")
    print("   - 如果没有足够改善，增加等待计数器")
    print("2. 判断阶段:")
    print(f"   - 当等待计数器达到{args.patience}个epoch时")
    print("   - 自动触发早停，停止训练")
    print("   - 保存最佳模型并恢复最佳权重")
    print()
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    
    print("🎉 早停策略继续训练完成!")
    print(f"📁 检查输出目录: {args.output}")
    print("📄 查看日志文件了解详细的早停信息")
    return 0

if __name__ == '__main__':
    exit(main())
