# vHeat-Tiny模型配置（带热辐射机制）- ImageNet100版本 - 早停微调版本
# 专门为早停策略优化的配置

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_tiny_with_radiation_imagenet100_early_stop
  NUM_CLASSES: 100
  DROP_PATH_RATE: 0.05  # 降低dropout以稳定微调

  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.08

  # vHeat特定参数
  VHEAT:
    PATCH_SIZE: 4
    IN_CHANS: 3
    EMBED_DIM: 96
    DEPTHS: [2, 2, 6, 2]
    MLP_RATIO: 4.0
    PATCH_NORM: True
    POST_NORM: True
    LAYER_SCALE: null

# 数据配置
DATA:
  BATCH_SIZE: 128
  DATA_PATH: '/path/to/imagenet100'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 4

# 数据增强配置 - 微调时使用更轻的数据增强
AUG:
  AUTO_AUGMENT: 'rand-m5-mstd0.5-inc1'
  REPROB: 0.1
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.4
  CUTMIX: 0.6
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置 - 早停微调专用
TRAIN:
  START_EPOCH: 0
  EPOCHS: 50  # 继续训练50轮，早停会在合适时机停止
  WARMUP_EPOCHS: 5
  WEIGHT_DECAY: 0.02
  BASE_LR: 1e-4  # 微调学习率
  WARMUP_LR: 1e-7
  MIN_LR: 1e-7
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 1
  USE_CHECKPOINT: False
  
  # EMA - 禁用EMA避免恢复问题
  MODEL_EMA: False
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False
  SHUFFLE: False

# 其他配置
OUTPUT: './output_early_stop'
PRINT_FREQ: 20
SAVE_FREQ: 5  # 更频繁地保存检查点
SEED: 42
TAG: 'vheat_tiny_with_radiation_imagenet100_early_stop'

# 混合精度训练
AMP_ENABLE: True
AMP_OPT_LEVEL: ''
ENABLE_AMP: False

# 其他设置
EVAL_MODE: False
FUSED_LAYERNORM: False
FUSED_WINDOW_PROCESS: False
THROUGHPUT_MODE: False
