# vHeat-Base模型配置（带热辐射机制）- ImageNet100版本

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_base_with_radiation_imagenet100
  NUM_CLASSES: 100
  DROP_PATH_RATE: 0.2  # Base模型更高的dropout

  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.12  # Base模型可以用更高的辐射强度

  # vHeat特定参数
  VHEAT:
    PATCH_SIZE: 4
    IN_CHANS: 3
    EMBED_DIM: 128  # Base版本使用更大的嵌入维度
    DEPTHS: [2, 2, 18, 2]  # Base版本的深度
    MLP_RATIO: 4.0
    PATCH_NORM: True
    POST_NORM: True
    LAYER_SCALE: null

# 数据配置
DATA:
  BATCH_SIZE: 64  # Base模型进一步减少批量大小
  DATA_PATH: '/path/to/imagenet100'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 4
  
# 数据增强配置
AUG:
  AUTO_AUGMENT: 'rand-m9-mstd0.5-inc1'
  REPROB: 0.3
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.8
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置
TRAIN:
  START_EPOCH: 0
  EPOCHS: 150  # Base模型训练更多轮
  WARMUP_EPOCHS: 20
  WEIGHT_DECAY: 0.08
  BASE_LR: 1e-3  # Base模型更低的学习率
  WARMUP_LR: 1e-6
  MIN_LR: 1e-6
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 2  # Base模型使用梯度累积
  USE_CHECKPOINT: True
  
  # EMA - 禁用EMA
  MODEL_EMA: False
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False

# 输出配置
OUTPUT: './output'
TAG: 'vheat_base_with_radiation_imagenet100'
SAVE_FREQ: 10
PRINT_FREQ: 20

# 其他配置
SEED: 42
EVAL_MODE: False
THROUGHPUT_MODE: False
