# vHeat ImageNet-1K 训练指南

本指南介绍如何使用 vHeat 模型在标准 ImageNet-1K 数据集上进行训练，包含早停机制的完整实现。

## 📋 目录

- [环境要求](#环境要求)
- [数据集准备](#数据集准备)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [早停机制](#早停机制)
- [训练参数](#训练参数)
- [使用示例](#使用示例)
- [性能优化](#性能优化)
- [故障排除](#故障排除)

## 🔧 环境要求

- Python 3.8+
- PyTorch 1.12+
- CUDA 11.3+ (推荐)
- 至少 16GB GPU 内存 (对于 tiny 模型)
- 至少 32GB GPU 内存 (对于 small/base 模型)

## 📁 数据集准备

### ImageNet-1K 数据集结构

确保您的 ImageNet-1K 数据集按以下结构组织：

```
/path/to/imagenet/
├── train/
│   ├── n01440764/
│   │   ├── n01440764_10026.JPEG
│   │   ├── n01440764_10027.JPEG
│   │   └── ...
│   ├── n01443537/
│   └── ... (共1000个类别文件夹)
└── val/
    ├── n01440764/
    │   ├── ILSVRC2012_val_00000293.JPEG
    │   ├── ILSVRC2012_val_00002138.JPEG
    │   └── ...
    ├── n01443537/
    └── ... (共1000个类别文件夹)
```

### 数据集验证

脚本会自动验证数据集结构：
- 检查 train 和 val 目录是否存在
- 验证类别数量是否为 1000
- 确认 train 和 val 的类别数量一致

## 🚀 快速开始

### 基础训练 (不带早停)

```bash
# Tiny 模型训练
python train_imagenet1k.py \
    --data-path /path/to/imagenet \
    --model-size tiny \
    --batch-size 128 \
    --gpus 4 \
    --output ./output_tiny

# Small 模型训练
python train_imagenet1k.py \
    --data-path /path/to/imagenet \
    --model-size small \
    --batch-size 64 \
    --gpus 8 \
    --output ./output_small

# Base 模型训练
python train_imagenet1k.py \
    --data-path /path/to/imagenet \
    --model-size base \
    --batch-size 32 \
    --gpus 8 \
    --output ./output_base
```

### 带早停机制的训练

```bash
python train_imagenet1k.py \
    --data-path /path/to/imagenet \
    --model-size tiny \
    --batch-size 128 \
    --gpus 4 \
    --early-stopping \
    --patience 20 \
    --min-delta 0.0001 \
    --output ./output_with_early_stopping
```

### 启用热辐射机制

```bash
python train_imagenet1k.py \
    --data-path /path/to/imagenet \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.05 \
    --early-stopping \
    --output ./output_radiation
```

## ⚙️ 配置说明

### 模型配置

| 模型大小 | 嵌入维度 | 深度 | 参数量 | 推荐批大小 |
|---------|---------|------|--------|-----------|
| tiny    | 96      | [2,2,6,2] | ~28M | 128 |
| small   | 192     | [2,2,18,2] | ~50M | 64 |
| base    | 384     | [2,2,18,2] | ~88M | 32 |

### 训练配置

- **学习率**: 5e-4 (默认)
- **权重衰减**: 0.05
- **训练轮数**: 300 (ImageNet-1K 推荐)
- **预热轮数**: 20
- **学习率调度**: Cosine Annealing
- **优化器**: AdamW

### 数据增强

- **MixUp**: 0.8
- **CutMix**: 1.0
- **AutoAugment**: rand-m9-mstd0.5-inc1
- **Random Erasing**: 0.25

## 🛑 早停机制

### 早停参数

- `--early-stopping`: 启用早停机制
- `--patience`: 耐心等待轮数 (默认: 20)
- `--min-delta`: 最小改善阈值 (默认: 0.0001, 即 0.01%)

### 早停工作原理

1. **监控指标**: 验证集准确率 (Top-1 Accuracy)
2. **改善判断**: 当前准确率 > 最佳准确率 + min_delta
3. **耐心机制**: 连续 `patience` 轮无改善时停止训练
4. **权重恢复**: 自动恢复到最佳验证准确率对应的模型权重

### 早停日志示例

```
EarlyStopping: New best score: 0.7234, resetting patience counter
EarlyStopping: No improvement (current: 0.7231, best: 0.7234, improvement: -0.0003, min_delta: 0.0001), patience: 1/20
EarlyStopping: Stopping training at epoch 156
EarlyStopping: Best score was 0.7234 at epoch 145
```

## 📊 训练参数

### 完整参数列表

```bash
python train_imagenet1k.py \
    --data-path /path/to/imagenet \          # 数据集路径 (必需)
    --batch-size 64 \                        # 每GPU批大小
    --output ./output \                       # 输出目录
    --gpus 4 \                               # GPU数量
    --model-size tiny \                       # 模型大小 [tiny|small|base]
    --enable-radiation \                      # 启用热辐射机制
    --radiation-strength 0.05 \               # 辐射强度
    --epochs 300 \                           # 训练轮数
    --lr 5e-4 \                              # 学习率
    --weight-decay 0.05 \                    # 权重衰减
    --warmup-epochs 20 \                     # 预热轮数
    --early-stopping \                       # 启用早停
    --patience 20 \                          # 早停耐心
    --min-delta 0.0001 \                     # 早停阈值
    --resume /path/to/checkpoint.pth \       # 恢复训练
    --pretrained /path/to/pretrained.pth \   # 预训练权重
    --single-gpu \                           # 单GPU训练
    --disable-ema                            # 禁用EMA
```

## 💡 使用示例

### 示例 1: 标准训练

```bash
# 使用 4 个 GPU 训练 tiny 模型
python train_imagenet1k.py \
    --data-path /data/imagenet \
    --model-size tiny \
    --batch-size 128 \
    --gpus 4 \
    --output ./experiments/tiny_standard
```

### 示例 2: 带早停的训练

```bash
# 启用早停机制，耐心等待 15 轮
python train_imagenet1k.py \
    --data-path /data/imagenet \
    --model-size small \
    --batch-size 64 \
    --gpus 8 \
    --early-stopping \
    --patience 15 \
    --output ./experiments/small_early_stop
```

### 示例 3: 热辐射 + 早停

```bash
# 同时启用热辐射机制和早停
python train_imagenet1k.py \
    --data-path /data/imagenet \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.05 \
    --early-stopping \
    --patience 20 \
    --output ./experiments/tiny_radiation_early_stop
```

### 示例 4: 从检查点恢复训练

```bash
# 从之前的检查点恢复训练
python train_imagenet1k.py \
    --data-path /data/imagenet \
    --model-size base \
    --resume ./experiments/base_model/ckpt_epoch_100.pth \
    --early-stopping \
    --output ./experiments/base_resumed
```

### 示例 5: 单GPU训练

```bash
# 在单个GPU上训练 (适合调试)
python train_imagenet1k.py \
    --data-path /data/imagenet \
    --model-size tiny \
    --batch-size 64 \
    --single-gpu \
    --early-stopping \
    --output ./experiments/tiny_single_gpu
```

## 🔧 性能优化

### GPU 内存优化

1. **调整批大小**: 根据GPU内存调整 `--batch-size`
2. **梯度累积**: 如果内存不足，可以减小批大小并增加累积步数
3. **混合精度**: 自动启用 AMP (Automatic Mixed Precision)

### 训练速度优化

1. **数据加载**: 调整 `NUM_WORKERS` (默认: 8)
2. **Pin Memory**: 默认启用 `PIN_MEMORY`
3. **多GPU训练**: 使用 `torchrun` 进行分布式训练

### 推荐配置

| GPU 型号 | 推荐模型 | 批大小 | GPU数量 |
|---------|---------|--------|---------|
| RTX 3090 | tiny | 128 | 2-4 |
| RTX 4090 | small | 64 | 4-8 |
| A100 40GB | base | 32 | 8 |
| A100 80GB | base | 64 | 4-8 |

## 🔍 故障排除

### 常见问题

1. **CUDA 内存不足**
   ```bash
   # 减小批大小
   --batch-size 32
   ```

2. **数据集路径错误**
   ```
   ValueError: Dataset path does not exist: /path/to/imagenet
   ```
   检查数据集路径是否正确

3. **类别数量不匹配**
   ```
   Warning: Expected 1000 classes for ImageNet-1K, but found 100
   ```
   确认使用的是 ImageNet-1K 而不是 ImageNet-100

4. **分布式训练失败**
   ```bash
   # 使用单GPU模式调试
   --single-gpu
   ```

### 日志分析

训练日志保存在 `{output}/log.txt`，包含：
- 训练/验证损失和准确率
- 学习率变化
- 早停状态
- 模型检查点信息

### 检查点管理

- 自动保存: 每 10 轮保存一次检查点
- 最佳模型: 早停时自动保存最佳模型
- 恢复训练: 使用 `--resume` 参数恢复

## 📈 预期性能

### ImageNet-1K Top-1 准确率

| 模型 | 无辐射 | 带辐射 | 训练时间 (8xA100) |
|------|--------|--------|-------------------|
| Tiny | ~76.5% | ~77.2% | ~12 小时 |
| Small | ~81.2% | ~81.8% | ~24 小时 |
| Base | ~83.1% | ~83.6% | ~48 小时 |

*注: 实际性能可能因硬件配置和数据增强策略而有所差异*

## 📂 文件结构

训练完成后，输出目录结构如下：

```
output_imagenet1k/
├── config.json                    # 训练配置
├── log.txt                       # 训练日志
├── ckpt_epoch_10.pth             # 定期检查点
├── ckpt_epoch_20.pth
├── ...
├── best.pth                      # 最佳模型 (基于验证准确率)
├── best_early_stop.pth           # 早停最佳模型 (如果启用早停)
└── last.pth                      # 最后一轮模型
```

## 🔄 训练监控

### 日志分析

训练日志包含以下关键信息：
- 每轮的训练/验证损失和准确率
- 学习率变化
- GPU内存使用情况
- 早停状态更新

### 关键指标

监控以下指标来评估训练进展：
- **Top-1 Accuracy**: 主要评估指标
- **Top-5 Accuracy**: 辅助评估指标
- **Training Loss**: 训练损失趋势
- **Learning Rate**: 学习率调度

### TensorBoard 支持

如果需要可视化训练过程，可以添加 TensorBoard 支持：

```bash
# 在训练目录中启动 TensorBoard
tensorboard --logdir ./experiments
```

## 🎯 性能调优建议

### 超参数调优

1. **学习率**: 从 5e-4 开始，根据收敛情况调整
2. **批大小**: 在GPU内存允许的情况下尽可能大
3. **权重衰减**: 0.05-0.1 之间通常效果较好
4. **早停耐心**: 15-25 轮适合大多数情况

### 数据增强调优

根据具体需求调整数据增强策略：
- 减少 MixUp/CutMix 强度可能提高收敛速度
- 调整 AutoAugment 策略以适应特定数据分布

### 模型选择建议

| 场景 | 推荐模型 | 理由 |
|------|---------|------|
| 快速验证 | Tiny | 训练速度快，资源需求低 |
| 平衡性能 | Small | 性能与效率的良好平衡 |
| 最佳性能 | Base | 最高准确率，需要更多资源 |

## 📝 注意事项

1. **数据集版权**: 确保您有使用 ImageNet-1K 数据集的合法权限
2. **计算资源**: ImageNet-1K 训练需要大量计算资源，建议使用多GPU
3. **存储空间**: 确保有足够的存储空间保存模型检查点和日志
4. **早停调优**: 根据具体任务调整早停参数以获得最佳性能
5. **内存管理**: 大模型训练时注意GPU内存使用，必要时减小批大小
6. **训练时间**: 完整训练可能需要数天时间，建议使用早停机制

## 🚀 快速开始脚本

我们提供了 `examples_imagenet1k.sh` 脚本，包含多种训练场景的示例命令。使用方法：

```bash
# 修改脚本中的数据集路径
vim examples_imagenet1k.sh

# 运行示例 (仅显示命令，不实际执行)
bash examples_imagenet1k.sh
```

## 🤝 支持

如果您在使用过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看训练日志获取详细错误信息
3. 确认环境配置和数据集格式正确
4. 运行 `test_imagenet1k_config.py` 验证配置文件
5. 参考 `examples_imagenet1k.sh` 中的示例命令
