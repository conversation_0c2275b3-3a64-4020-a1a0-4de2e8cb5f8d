#!/usr/bin/env python3
"""
ImageNet-1K模型评估脚本
用于评估训练完成的模型在验证集上的性能
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Evaluate vHeat model on ImageNet-1K')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet-1K dataset')
    parser.add_argument('--model-path', type=str, required=True,
                        help='Path to trained model checkpoint')
    parser.add_argument('--model-size', type=str, default='tiny',
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--enable-radiation', action='store_true',
                        help='Enable thermal radiation mechanism (must match training config)')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size for evaluation (default: 64)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU evaluation (no distributed)')

    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        raise ValueError(f"Model checkpoint does not exist: {args.model_path}")
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    val_path = os.path.join(args.data_path, 'val')
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {val_classes} classes in validation set")
    
    if val_classes != 1000:
        print(f"Warning: Expected 1000 classes for ImageNet-1K, but found {val_classes}")
    
    # 选择配置文件
    if args.enable_radiation:
        # 使用热辐射版本的配置文件
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_with_radiation_imagenet1k.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_with_radiation_imagenet1k.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_with_radiation_imagenet1k.yaml'
    else:
        # 使用原始版本的配置文件
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet1k.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_imagenet1k.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_imagenet1k.yaml'
    
    # 构建评估命令
    if args.single_gpu or args.gpus == 1:
        # 单GPU评估
        cmd = [
            'python', 'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--resume', args.model_path,
            '--eval',  # 仅评估模式
            '--local_rank', '0'
        ]
    else:
        # 多GPU分布式评估
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=' + str(args.gpus),
            '--master_addr=127.0.0.1',
            '--master_port=29503',  # 使用不同的端口避免冲突
            'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--resume', args.model_path,
            '--eval'  # 仅评估模式
        ]
    
    # 添加自定义配置选项
    opts = [
        '--opts',
        'MODEL.NUM_CLASSES', str(val_classes)
    ]
    
    # 如果启用热辐射，添加辐射相关配置
    if args.enable_radiation:
        opts.extend([
            'MODEL.ENABLE_RADIATION', 'True'
        ])
    
    cmd.extend(opts)
    
    print("Running evaluation command:")
    print(' '.join(cmd))
    print()
    
    # 执行评估
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Evaluation completed successfully!")
        
        # 尝试从输出中提取准确率信息
        output_lines = result.stdout.split('\n')
        for line in output_lines:
            if 'Accuracy of the network on the' in line and 'test images:' in line:
                print(f"Final Result: {line.strip()}")
                break
        
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"Evaluation failed with error code {e.returncode}")
        print("Error output:")
        print(e.stderr)
        return e.returncode

if __name__ == '__main__':
    exit(main())


# 使用示例:
# 
# 评估基础模型:
# python eval_imagenet1k.py --data-path /path/to/imagenet --model-path ./output/best.pth --model-size tiny
#
# 评估热辐射模型:
# python eval_imagenet1k.py --data-path /path/to/imagenet --model-path ./output/best.pth --model-size tiny --enable-radiation
#
# 多GPU评估:
# python eval_imagenet1k.py --data-path /path/to/imagenet --model-path ./output/best.pth --model-size small --gpus 4
