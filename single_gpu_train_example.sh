#!/bin/bash

# 单GPU训练示例脚本 - 热辐射 + 早停
# 适用于单个GPU环境

# 设置数据集路径
IMAGENET_PATH="/root/lanyun-tmp/imagenet1k"

echo "检查数据集结构..."
echo "数据集路径: $IMAGENET_PATH"

# 检查数据集结构
if [ ! -d "$IMAGENET_PATH" ]; then
    echo "错误: 数据集路径不存在: $IMAGENET_PATH"
    exit 1
fi

if [ ! -d "$IMAGENET_PATH/train" ]; then
    echo "错误: 训练集目录不存在: $IMAGENET_PATH/train"
    exit 1
fi

if [ ! -d "$IMAGENET_PATH/val" ]; then
    echo "错误: 验证集目录不存在: $IMAGENET_PATH/val"
    echo "请检查验证集目录名称，可能是 'validation' 而不是 'val'"
    
    # 检查是否存在其他可能的验证集目录名
    if [ -d "$IMAGENET_PATH/validation" ]; then
        echo "发现 validation 目录，建议创建软链接:"
        echo "ln -s $IMAGENET_PATH/validation $IMAGENET_PATH/val"
    elif [ -d "$IMAGENET_PATH/test" ]; then
        echo "发现 test 目录，建议创建软链接:"
        echo "ln -s $IMAGENET_PATH/test $IMAGENET_PATH/val"
    fi
    exit 1
fi

# 显示数据集信息
echo "训练集类别数: $(ls -1 $IMAGENET_PATH/train | wc -l)"
echo "验证集类别数: $(ls -1 $IMAGENET_PATH/val | wc -l)"

echo ""
echo "开始单GPU训练 - 热辐射 + 早停..."
echo "================================"

# 单GPU训练命令
python train_imagenet1k.py \
    --data-path /root/lanyun-tmp/imagenet1k \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.15 \
    --early-stopping \
    --patience 15 \
    --min-delta 0.0001 \
    --batch-size 256 \
    --single-gpu \
    --epochs 300 \
    --lr 5e-4 \
    --weight-decay 0.05 \
    --warmup-epochs 20 \
    --output ./experiments/tiny_radiation_early_stop_single_gpu

echo ""
echo "训练完成!"
