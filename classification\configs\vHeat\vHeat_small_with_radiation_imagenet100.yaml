# vHeat-Small模型配置（带热辐射机制）- ImageNet100版本

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_small_with_radiation_imagenet100
  NUM_CLASSES: 100
  DROP_PATH_RATE: 0.15  # Small模型稍高的dropout

  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.1  # Small模型可以用稍高的辐射强度

  # vHeat特定参数
  VHEAT:
    PATCH_SIZE: 4
    IN_CHANS: 3
    EMBED_DIM: 96
    DEPTHS: [2, 2, 18, 2]  # Small版本的深度
    MLP_RATIO: 4.0
    PATCH_NORM: True
    POST_NORM: True
    LAYER_SCALE: null

# 数据配置
DATA:
  BATCH_SIZE: 96  # Small模型减少批量大小
  DATA_PATH: '/path/to/imagenet100'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 4
  
# 数据增强配置
AUG:
  AUTO_AUGMENT: 'rand-m8-mstd0.5-inc1'
  REPROB: 0.25
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.7
  CUTMIX: 0.9
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置
TRAIN:
  START_EPOCH: 0
  EPOCHS: 120  # Small模型训练更多轮
  WARMUP_EPOCHS: 15
  WEIGHT_DECAY: 0.06
  BASE_LR: 1.5e-3  # 稍低的学习率
  WARMUP_LR: 1e-6
  MIN_LR: 1e-6
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 1
  USE_CHECKPOINT: False
  
  # EMA - 禁用EMA
  MODEL_EMA: False
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False

# 输出配置
OUTPUT: './output'
TAG: 'vheat_small_with_radiation_imagenet100'
SAVE_FREQ: 10
PRINT_FREQ: 20

# 其他配置
SEED: 42
EVAL_MODE: False
THROUGHPUT_MODE: False
