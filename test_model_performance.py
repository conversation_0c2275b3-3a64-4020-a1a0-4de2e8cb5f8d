#!/usr/bin/env python3
"""
测试训练好的热辐射vHeat模型在验证集上的性能
输出Top-1和Top-5准确率
"""
import os
import sys
import time
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import argparse
from tqdm import tqdm
import numpy as np

# 添加classification路径
sys.path.append('classification')

from models import build_model
from utils.config import get_config
import torchvision.transforms as transforms
import torchvision.datasets as datasets


class AverageMeter:
    """计算和存储平均值和当前值"""
    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


def accuracy(output, target, topk=(1,)):
    """计算指定topk的准确率"""
    with torch.no_grad():
        maxk = max(topk)
        batch_size = target.size(0)

        _, pred = output.topk(maxk, 1, True, True)
        pred = pred.t()
        correct = pred.eq(target.view(1, -1).expand_as(pred))

        res = []
        for k in topk:
            correct_k = correct[:k].reshape(-1).float().sum(0, keepdim=True)
            res.append(correct_k.mul_(100.0 / batch_size))
        return res


def create_data_loader(data_path, batch_size=128, num_workers=4, img_size=224):
    """创建数据加载器"""

    # 验证集的数据变换
    transform = transforms.Compose([
        transforms.Resize(int(img_size * 256 / 224)),  # 256 for 224
        transforms.CenterCrop(img_size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                           std=[0.229, 0.224, 0.225])
    ])

    # 验证集路径
    val_dir = os.path.join(data_path, 'val')
    if not os.path.exists(val_dir):
        raise FileNotFoundError(f"验证集目录不存在: {val_dir}")

    # 创建数据集
    dataset = datasets.ImageFolder(val_dir, transform=transform)

    # 创建数据加载器
    data_loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    return dataset, data_loader


def load_model_checkpoint(model, checkpoint_path):
    """加载模型检查点"""
    print(f"=> 加载检查点: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 检查检查点内容
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
        print(f"=> 检查点包含模型权重，轮次: {checkpoint.get('epoch', 'unknown')}")
    else:
        # 如果检查点直接是state_dict
        state_dict = checkpoint
        print("=> 检查点直接包含模型权重")
    
    # 处理DataParallel包装的模型
    if list(state_dict.keys())[0].startswith('module.'):
        new_state_dict = {}
        for k, v in state_dict.items():
            new_state_dict[k[7:]] = v  # 移除'module.'前缀
        state_dict = new_state_dict
    
    # 加载权重
    missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
    
    if missing_keys:
        print(f"⚠️  缺失的键: {missing_keys}")
    if unexpected_keys:
        print(f"⚠️  意外的键: {unexpected_keys}")
    
    print("✅ 模型权重加载成功!")
    return model


@torch.no_grad()
def validate_model(model, data_loader, device):
    """验证模型性能"""
    model.eval()
    
    top1 = AverageMeter()
    top5 = AverageMeter()
    batch_time = AverageMeter()
    
    print(f"\n🔍 开始验证，共 {len(data_loader)} 个批次...")
    
    end = time.time()
    for idx, (images, targets) in enumerate(tqdm(data_loader, desc="验证中")):
        images = images.to(device, non_blocking=True)
        targets = targets.to(device, non_blocking=True)
        
        # 前向传播
        outputs = model(images)
        
        # 计算准确率
        acc1, acc5 = accuracy(outputs, targets, topk=(1, 5))
        
        batch_size = images.size(0)
        top1.update(acc1.item(), batch_size)
        top5.update(acc5.item(), batch_size)
        
        # 计算时间
        batch_time.update(time.time() - end)
        end = time.time()
        
        # 每100个批次打印一次进度
        if idx % 100 == 0:
            print(f'验证: [{idx}/{len(data_loader)}] '
                  f'时间 {batch_time.val:.3f} ({batch_time.avg:.3f}) '
                  f'Acc@1 {top1.val:.3f} ({top1.avg:.3f}) '
                  f'Acc@5 {top5.val:.3f} ({top5.avg:.3f})')
    
    print(f'\n📊 验证结果:')
    print(f'   * Top-1 准确率: {top1.avg:.3f}%')
    print(f'   * Top-5 准确率: {top5.avg:.3f}%')
    print(f'   * 平均批次时间: {batch_time.avg:.3f}s')
    print(f'   * 总验证时间: {batch_time.sum:.1f}s')
    
    return top1.avg, top5.avg


def main():
    parser = argparse.ArgumentParser('热辐射vHeat模型性能测试')
    parser.add_argument('--cfg', type=str, required=True, help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--data-path', type=str, required=True, help='数据集路径')
    parser.add_argument('--batch-size', type=int, default=256, help='批量大小')
    parser.add_argument('--num-workers', type=int, default=4, help='数据加载器工作进程数')
    parser.add_argument('--device', type=str, default='cuda', help='设备 (cuda/cpu)')
    
    args = parser.parse_args()
    
    print("🔥 热辐射vHeat模型性能测试")
    print("=" * 60)
    print(f"配置文件: {args.cfg}")
    print(f"检查点: {args.checkpoint}")
    print(f"数据路径: {args.data_path}")
    print(f"批量大小: {args.batch_size}")
    print(f"设备: {args.device}")
    
    # 设置设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        print("⚠️  CUDA不可用，切换到CPU")
        device = torch.device('cpu')
    else:
        device = torch.device(args.device)
    
    print(f"使用设备: {device}")
    
    # 加载配置
    print(f"\n📋 加载配置...")
    config_args = argparse.Namespace(
        cfg=args.cfg,
        local_rank=0,
        opts=[
            'DATA.DATA_PATH', args.data_path,
            'DATA.BATCH_SIZE', str(args.batch_size),
            'DATA.NUM_WORKERS', str(args.num_workers),
            'LOCAL_RANK', '0'  # 设置为单GPU模式
        ]
    )
    config = get_config(config_args)

    # 设置为非分布式模式
    config.defrost()
    config.LOCAL_RANK = 0
    config.freeze()
    
    print(f"   模型类型: {config.MODEL.TYPE}")
    print(f"   类别数: {config.MODEL.NUM_CLASSES}")
    print(f"   图像大小: {config.DATA.IMG_SIZE}")
    print(f"   热辐射启用: {getattr(config.MODEL, 'ENABLE_RADIATION', False)}")
    
    # 构建模型
    print(f"\n🏗️  构建模型...")
    model = build_model(config)
    model.to(device)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"   总参数数: {total_params:,}")
    print(f"   可训练参数数: {trainable_params:,}")
    
    # 加载检查点
    model = load_model_checkpoint(model, args.checkpoint)
    
    # 构建数据加载器
    print(f"\n📁 构建数据加载器...")
    dataset_val, data_loader_val = create_data_loader(
        args.data_path,
        args.batch_size,
        args.num_workers,
        config.DATA.IMG_SIZE
    )

    print(f"   验证集大小: {len(dataset_val)}")
    print(f"   验证批次数: {len(data_loader_val)}")
    print(f"   类别数: {len(dataset_val.classes)}")
    
    # 验证模型
    print(f"\n🚀 开始验证...")
    start_time = time.time()
    
    top1_acc, top5_acc = validate_model(model, data_loader_val, device)
    
    total_time = time.time() - start_time
    
    # 输出最终结果
    print(f"\n" + "=" * 60)
    print(f"🎉 验证完成!")
    print(f"📊 最终结果:")
    print(f"   * Top-1 准确率: {top1_acc:.3f}%")
    print(f"   * Top-5 准确率: {top5_acc:.3f}%")
    print(f"   * 总验证时间: {total_time:.1f}s")
    print(f"   * 平均每样本时间: {total_time/len(dataset_val)*1000:.2f}ms")
    
    # 保存结果到文件
    result_file = f"validation_results_{int(time.time())}.txt"
    with open(result_file, 'w') as f:
        f.write(f"热辐射vHeat模型验证结果\n")
        f.write(f"=" * 40 + "\n")
        f.write(f"配置文件: {args.cfg}\n")
        f.write(f"检查点: {args.checkpoint}\n")
        f.write(f"数据路径: {args.data_path}\n")
        f.write(f"模型类型: {config.MODEL.TYPE}\n")
        f.write(f"类别数: {config.MODEL.NUM_CLASSES}\n")
        f.write(f"总参数数: {total_params:,}\n")
        f.write(f"验证集大小: {len(dataset_val)}\n")
        f.write(f"Top-1 准确率: {top1_acc:.3f}%\n")
        f.write(f"Top-5 准确率: {top5_acc:.3f}%\n")
        f.write(f"验证时间: {total_time:.1f}s\n")
    
    print(f"📝 结果已保存到: {result_file}")


if __name__ == '__main__':
    main()
