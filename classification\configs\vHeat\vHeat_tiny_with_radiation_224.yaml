# vHeat-Tiny模型配置（带热辐射机制）
# 基于原始vHeat-Tiny配置，添加热辐射功能

MODEL:
  TYPE: vHeatWithRadiation
  NAME: vHeat_tiny_with_radiation
  
  # 基础模型参数
  PATCH_SIZE: 4
  IN_CHANS: 3
  NUM_CLASSES: 1000
  EMBED_DIM: 96
  DEPTHS: [2, 2, 9, 2]
  DIMS: [96, 192, 384, 768]
  
  # 训练相关参数
  DROP_PATH_RATE: 0.2
  PATCH_NORM: True
  POST_NORM: True
  LAYER_SCALE: null
  USE_CHECKPOINT: False
  MLP_RATIO: 4.0
  
  # 热辐射相关参数
  ENABLE_RADIATION: True
  RADIATION_STRENGTH: 0.1  # 辐射强度，可调节
  
  # 推理模式
  INFER_MODE: False

# 数据配置
DATA:
  BATCH_SIZE: 128
  DATA_PATH: '/path/to/imagenet'
  DATASET: 'imagenet'
  IMG_SIZE: 224
  INTERPOLATION: 'bicubic'
  ZIP_MODE: False
  CACHE_MODE: 'part'
  PIN_MEMORY: True
  NUM_WORKERS: 8
  
  # 数据增强
  AUTO_AUGMENT: 'rand-m9-mstd0.5-inc1'
  REPROB: 0.25
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.8
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

# 训练配置
TRAIN:
  START_EPOCH: 0
  EPOCHS: 300
  WARMUP_EPOCHS: 20
  WEIGHT_DECAY: 0.05
  BASE_LR: 4e-3
  WARMUP_LR: 5e-7
  MIN_LR: 5e-6
  
  # 优化器
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9
  
  # 学习率调度
  LR_SCHEDULER:
    NAME: 'cosine'
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    MULTISTEPS: []
  
  # 正则化
  CLIP_GRAD: 5.0
  AUTO_RESUME: True
  ACCUMULATION_STEPS: 1
  USE_CHECKPOINT: False
  
  # EMA (Exponential Moving Average)
  MODEL_EMA: True
  MODEL_EMA_DECAY: 0.9999
  MODEL_EMA_FORCE_CPU: False

# 验证配置
TEST:
  CROP: True
  SEQUENTIAL: False

# 输出配置
OUTPUT: './output'
TAG: 'vheat_tiny_with_radiation'
SAVE_FREQ: 1
PRINT_FREQ: 10

# 其他配置
SEED: 0
EVAL_MODE: False
THROUGHPUT_MODE: False

# 热辐射机制的详细说明
# RADIATION_STRENGTH: 控制热辐射的强度
#   - 0.0: 关闭热辐射，等同于原始vHeat
#   - 0.05-0.1: 轻微辐射，适合初始实验
#   - 0.1-0.2: 中等辐射，平衡性能和稳定性
#   - 0.2+: 强辐射，可能需要调整学习率

# 实验建议：
# 1. 首先使用 RADIATION_STRENGTH: 0.1 进行基线实验
# 2. 如果效果好，可以尝试 0.15-0.2
# 3. 如果训练不稳定，降低到 0.05-0.08
# 4. 可以在不同层使用不同的辐射强度（需要修改代码）
