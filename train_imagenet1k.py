#!/usr/bin/env python3
"""
ImageNet-1K训练脚本 (带早停机制)
使用方法:
python train_imagenet1k.py --data-path /path/to/your/imagenet --batch-size 64 --output ./output
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Train vHeat on ImageNet-1K with Early Stopping')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet-1K dataset')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size per GPU (default: 64)')
    parser.add_argument('--output', type=str, default='./output_imagenet1k',
                        help='Output directory (default: ./output_imagenet1k)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--model-size', type=str, default='tiny',
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--enable-radiation', action='store_true',
                        help='Enable thermal radiation mechanism')
    parser.add_argument('--radiation-strength', type=float, default=0.05,
                        help='Radiation strength (default: 0.05 for ImageNet-1K)')
    parser.add_argument('--epochs', type=int, default=300,
                        help='Number of training epochs (default: 300)')
    parser.add_argument('--resume', type=str, default='',
                        help='Resume from checkpoint')
    parser.add_argument('--pretrained', type=str, default='',
                        help='Use pretrained weights')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU training (no distributed)')
    parser.add_argument('--disable-ema', action='store_true',
                        help='Disable EMA (useful for resuming from problematic checkpoints)')
    
    # 早停相关参数
    parser.add_argument('--early-stopping', action='store_true',
                        help='Enable early stopping mechanism')
    parser.add_argument('--patience', type=int, default=20,
                        help='Early stopping patience (default: 20)')
    parser.add_argument('--min-delta', type=float, default=0.0001,
                        help='Minimum improvement threshold for early stopping (default: 0.0001)')
    
    # 学习率和优化器参数
    parser.add_argument('--lr', type=float, default=5e-4,
                        help='Learning rate (default: 5e-4)')
    parser.add_argument('--weight-decay', type=float, default=0.05,
                        help='Weight decay (default: 0.05)')
    parser.add_argument('--warmup-epochs', type=int, default=20,
                        help='Warmup epochs (default: 20)')

    args = parser.parse_args()
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    print(f"Found {val_classes} classes in validation set")
    
    if train_classes != val_classes:
        print(f"Warning: Train and validation have different number of classes!")
    
    # 验证是否为ImageNet-1K (应该有1000个类别)
    if train_classes != 1000:
        print(f"Warning: Expected 1000 classes for ImageNet-1K, but found {train_classes}")
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            return 1
    
    # 选择配置文件
    if args.enable_radiation:
        # 使用热辐射版本的配置文件
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_with_radiation_imagenet1k.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_with_radiation_imagenet1k.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_with_radiation_imagenet1k.yaml'
    else:
        # 使用原始版本的配置文件
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet1k.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_imagenet1k.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_imagenet1k.yaml'
    
    # 构建训练命令
    if args.early_stopping:
        # 使用带早停的训练脚本
        main_script = 'classification/main_with_early_stopping.py'
    else:
        # 使用标准训练脚本
        main_script = 'classification/main.py'
    
    if args.single_gpu or args.gpus == 1:
        # 单GPU训练，不使用分布式
        cmd = [
            'python', main_script,
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--local_rank', '0'
        ]
    else:
        # 多GPU分布式训练，使用torchrun
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=' + str(args.gpus),
            '--master_addr=127.0.0.1',
            '--master_port=29502',  # 使用不同的端口避免冲突
            main_script,
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output
        ]
    
    # 添加可选参数
    if args.resume:
        cmd.extend(['--resume', args.resume])
    
    if args.pretrained:
        cmd.extend(['--pretrained', args.pretrained])
    
    if args.eval_only:
        cmd.append('--eval')

    if args.disable_ema:
        cmd.extend(['--model_ema', 'False'])

    # 添加自定义配置选项
    opts = [
        '--opts',
        'TRAIN.EPOCHS', str(args.epochs),
        'TRAIN.BASE_LR', str(args.lr),
        'TRAIN.WEIGHT_DECAY', str(args.weight_decay),
        'TRAIN.WARMUP_EPOCHS', str(args.warmup_epochs),
        'MODEL.NUM_CLASSES', str(train_classes)
    ]

    # 如果启用热辐射，添加辐射相关配置
    if args.enable_radiation:
        opts.extend([
            'MODEL.ENABLE_RADIATION', 'True',
            'MODEL.RADIATION_STRENGTH', str(args.radiation_strength)
        ])
    
    # 如果启用早停，添加早停相关配置
    if args.early_stopping:
        opts.extend([
            'TRAIN.EARLY_STOPPING.ENABLE', 'True',
            'TRAIN.EARLY_STOPPING.PATIENCE', str(args.patience),
            'TRAIN.EARLY_STOPPING.MIN_DELTA', str(args.min_delta)
        ])

    cmd.extend(opts)
    
    print("Running command:")
    print(' '.join(cmd))
    print()
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    
    print("Training completed successfully!")
    return 0

if __name__ == '__main__':
    exit(main())
