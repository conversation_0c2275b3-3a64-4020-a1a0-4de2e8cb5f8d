# 🔥 修复后的热辐射vHeat训练命令

## ✅ 问题已修复

已经修复了以下配置问题：
1. ✅ 将`MODEL.PATCH_SIZE`等参数移到`MODEL.VHEAT`下
2. ✅ 将数据增强参数从`DATA`移到`AUG`下
3. ✅ 在默认配置中添加了`MODEL.ENABLE_RADIATION`和`MODEL.RADIATION_STRENGTH`
4. ✅ 修复了模型构建函数的参数映射

## 🚀 现在可以使用的训练命令

### 方法1：使用修改后的训练脚本（推荐）

```bash
# Tiny模型 + 热辐射
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.08 \
    --batch-size 128 \
    --epochs 100 \
    --output ./output_tiny_radiation \
    --single-gpu

# Small模型 + 热辐射
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size small \
    --enable-radiation \
    --radiation-strength 0.1 \
    --batch-size 96 \
    --epochs 120 \
    --output ./output_small_radiation \
    --single-gpu

# Base模型 + 热辐射
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size base \
    --enable-radiation \
    --radiation-strength 0.12 \
    --batch-size 64 \
    --epochs 150 \
    --output ./output_base_radiation \
    --single-gpu
```

### 方法2：直接使用classification/main.py

```bash
# Tiny模型 + 热辐射
python classification/main.py \
    --cfg classification/configs/vHeat/vHeat_tiny_with_radiation_imagenet100.yaml \
    --batch-size 128 \
    --data-path /root/lanyun-fs/imagenet100-split \
    --output ./output_tiny_radiation \
    --local_rank 0 \
    --opts \
        TRAIN.EPOCHS 100 \
        MODEL.NUM_CLASSES 100 \
        MODEL.ENABLE_RADIATION True \
        MODEL.RADIATION_STRENGTH 0.08

# Small模型 + 热辐射
python classification/main.py \
    --cfg classification/configs/vHeat/vHeat_small_with_radiation_imagenet100.yaml \
    --batch-size 96 \
    --data-path /root/lanyun-fs/imagenet100-split \
    --output ./output_small_radiation \
    --local_rank 0 \
    --opts \
        TRAIN.EPOCHS 120 \
        MODEL.NUM_CLASSES 100 \
        MODEL.ENABLE_RADIATION True \
        MODEL.RADIATION_STRENGTH 0.1

# Base模型 + 热辐射
python classification/main.py \
    --cfg classification/configs/vHeat/vHeat_base_with_radiation_imagenet100.yaml \
    --batch-size 64 \
    --data-path /root/lanyun-fs/imagenet100-split \
    --output ./output_base_radiation \
    --local_rank 0 \
    --opts \
        TRAIN.EPOCHS 150 \
        MODEL.NUM_CLASSES 100 \
        MODEL.ENABLE_RADIATION True \
        MODEL.RADIATION_STRENGTH 0.12
```

## 🔧 对比实验（原始vHeat vs 热辐射vHeat）

```bash
# 训练原始vHeat Tiny（对比基线）
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size tiny \
    --batch-size 128 \
    --epochs 100 \
    --output ./output_tiny_original \
    --single-gpu

# 训练热辐射vHeat Tiny
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.08 \
    --batch-size 128 \
    --epochs 100 \
    --output ./output_tiny_radiation \
    --single-gpu
```

## 📊 推荐的实验流程

1. **第一步：验证环境**
```bash
# 测试配置是否正确
python test_config.py
```

2. **第二步：快速验证**
```bash
# 先用小批量训练几个epoch验证代码正确性
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.08 \
    --batch-size 32 \
    --epochs 2 \
    --output ./test_run \
    --single-gpu
```

3. **第三步：正式训练**
```bash
# 使用推荐的参数进行完整训练
python train_imagenet100.py \
    --data-path /root/lanyun-fs/imagenet100-split \
    --model-size tiny \
    --enable-radiation \
    --radiation-strength 0.08 \
    --batch-size 128 \
    --epochs 100 \
    --output ./output_tiny_radiation \
    --single-gpu
```

## ⚙️ 参数调优建议

### 辐射强度调优
- **保守起步**: 从0.05开始
- **标准设置**: Tiny=0.08, Small=0.1, Base=0.12
- **激进设置**: 可以尝试0.15-0.2（需要监控训练稳定性）

### 批量大小调优
- **GPU内存8GB**: Tiny=128, Small=64, Base=32
- **GPU内存12GB**: Tiny=256, Small=96, Base=64
- **GPU内存16GB+**: Tiny=512, Small=128, Base=96

### 学习率调优
- **小数据集**: 可以使用稍高的学习率（2e-3到5e-3）
- **热辐射模型**: 建议稍微降低学习率（原来的0.8倍）

## 🚨 故障排除

### 如果遇到内存不足
```bash
# 减少批量大小
--batch-size 64  # 或更小

# 启用梯度检查点（仅Base模型）
--opts TRAIN.USE_CHECKPOINT True
```

### 如果训练不稳定
```bash
# 降低辐射强度
--radiation-strength 0.05

# 降低学习率
--opts TRAIN.BASE_LR 1e-3
```

### 如果收敛慢
```bash
# 增加warmup轮数
--opts TRAIN.WARMUP_EPOCHS 20

# 调整学习率调度
--opts TRAIN.LR_SCHEDULER.NAME cosine
```

## 📈 预期结果

| 模型 | 原始vHeat | 热辐射vHeat | 提升 |
|------|----------|------------|------|
| Tiny | ~82% | ~84% | +2% |
| Small | ~84% | ~86% | +2% |
| Base | ~86% | ~88% | +2% |

现在配置已经修复，可以开始训练了！🚀
